version: "3.9"
services:
  app:
    build:
      dockerfile: Dockerfile
      context: .
    environment:
      DB_CONNECTION: mysql
      DB_HOST: db
      DB_PORT: 3306
      DB_DATABASE: e_brochure
      DB_USERNAME: root
      DB_PASSWORD: mysql-e-brochure
    command: php artisan octane:start --server=swoole --host=0.0.0.0 --port=8009
    ports:
      - "8009:8009"
    develop:
      watch:
        - action: sync+restart
          path: ./
          target: /app
    depends_on:
      - db
    volumes:
      - ./storage/app/qr-codes:/app/storage/app/qr-codes
      - ./storage/app/qr_codes:/app/storage/app/qr_codes

  db:
    image: mysql:8.0
    container_name: mysql_database
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: mysql-e-brochure
      MYSQL_DATABASE: e_brochure
      MYSQL_USER: laravel
      MYSQL_PASSWORD: mysql-e-brochure
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data:
